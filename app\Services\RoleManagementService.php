<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use App\Models\SupervisorAssistant;
use App\Models\SupervisorAssistantMapping;
use App\Models\UserRestriction;
use Illuminate\Support\Facades\DB;

class RoleManagementService
{
    /**
     * Assign Superior role to a user and manage their assistants.
     */
    public function assignSuperiorRole(User $user, array $assistantIds, User $assignedBy): bool
    {
        DB::beginTransaction();
        
        try {
            // Add Superior role
            $superiorRole = Role::where('name', 'Superior')->first();
            if ($superiorRole && !$user->roles->contains($superiorRole)) {
                $user->roles()->attach($superiorRole);
            }

            // Assign assistants
            $this->assignAssistants($user, $assistantIds, $assignedBy);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Assign assistants to a supervisor.
     * Uses the new mapping table that allows assistants to be assigned to multiple supervisors.
     * The old table is updated to reflect the most recent assignment for backward compatibility.
     */
    public function assignAssistants(User $supervisor, array $assistantIds, User $assignedBy): void
    {
        DB::beginTransaction();

        try {
            // Deactivate existing assignments for this supervisor only in the new table
            SupervisorAssistantMapping::where('supervisor_id', $supervisor->id)
                ->where('is_active', true)
                ->update(['is_active' => false]);

            // Create new assignments in the new table - allows multiple supervisors per assistant
            foreach ($assistantIds as $assistantId) {
                SupervisorAssistantMapping::updateOrCreate(
                    [
                        'supervisor_id' => $supervisor->id,
                        'assistant_id' => $assistantId,
                    ],
                    [
                        'assigned_by' => $assignedBy->id,
                        'is_active' => true,
                        'assigned_at' => now(),
                    ]
                );
            }

            // Update the old table for backward compatibility
            // The old table will show the most recent supervisor assignment due to unique constraint
            SupervisorAssistant::where('supervisor_id', $supervisor->id)
                ->where('is_active', true)
                ->update(['is_active' => false]);

            foreach ($assistantIds as $assistantId) {
                // Use updateOrCreate to handle the unique constraint in old table
                // This will update the assistant's "primary" supervisor in the old system
                SupervisorAssistant::updateOrCreate(
                    [
                        'assistant_id' => $assistantId,
                    ],
                    [
                        'supervisor_id' => $supervisor->id,
                        'assigned_by' => $assignedBy->id,
                        'is_active' => true,
                        'assigned_at' => now(),
                    ]
                );
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Remove Superior role and deactivate assistant relationships.
     */
    public function removeSuperiorRole(User $user): bool
    {
        DB::beginTransaction();
        
        try {
            // Remove Superior role
            $superiorRole = Role::where('name', 'Superior')->first();
            if ($superiorRole) {
                $user->roles()->detach($superiorRole);
            }

            // Deactivate all assistant relationships
            SupervisorAssistant::where('supervisor_id', $user->id)
                ->where('is_active', true)
                ->update(['is_active' => false]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Get all users eligible to be Superiors.
     * Includes users with mujeeb role, admin roles, or other management roles.
     * Now allows users who are already Superior to be shown (for cross-department assignments).
     */
    public function getEligibleSuperiors(): \Illuminate\Database\Eloquent\Collection
    {
        return User::where(function ($query) {
            // Include users with mujeeb role
            $query->whereHas('roles', function ($subQuery) {
                $subQuery->where('name', 'mujeeb');
            })
            // Include users with admin or management roles
            ->orWhereHas('roles', function ($subQuery) {
                $subQuery->whereIn('name', [
                    'Admin', 'Manager', 'Mufti', 'Checker', 
                    'Nazim', 'nazim-ul-umoor', 'Mahlenazar',
                    'Nazim_Viral', 'Shoba_Viral', 'Shoba_Viral_Iec'
                ]);
            })
            // Include users who already have Superior role (for cross-department assignments)
            ->orWhereHas('roles', function ($subQuery) {
                $subQuery->where('name', 'Superior');
            });
        })
        ->orderBy('name')
        ->get();
    }

    /**
     * Get all users eligible to be assistants.
     * Includes users with mujeeb role, operational roles, and even Superior users.
     * Allows cross-department assignments where a user can be Superior in one dept and Assistant in another.
     */
    public function getEligibleAssistants(): \Illuminate\Database\Eloquent\Collection
    {
        return User::where(function ($query) {
            // Include users with mujeeb role
            $query->whereHas('roles', function ($subQuery) {
                $subQuery->where('name', 'mujeeb');
            })
            // Include users with operational roles
            ->orWhereHas('roles', function ($subQuery) {
                $subQuery->whereIn('name', [
                    'Checker', 'Muawin', 'Nazim_Viral', 
                    'Shoba_Viral', 'Shoba_Viral_Iec', 'Manager'
                ]);
            })
            // Include users with admin roles (they can also be assistants in some departments)
            ->orWhereHas('roles', function ($subQuery) {
                $subQuery->whereIn('name', [
                    'Admin', 'Mufti', 'Nazim', 'nazim-ul-umoor', 'Mahlenazar'
                ]);
            })
            // Include users who already have Superior role (for cross-department assignments)
            ->orWhereHas('roles', function ($subQuery) {
                $subQuery->where('name', 'Superior');
            });
        })
        // Remove the restriction that excludes already assigned assistants
        // This allows more flexible assignments across departments
        ->orderBy('name')
        ->get();
    }

    /**
     * Get supervisor-assistant mappings.
     */
    public function getSupervisorAssistantMappings(): \Illuminate\Database\Eloquent\Collection
    {
        return SupervisorAssistant::with(['supervisor', 'assistant', 'assignedBy'])
            ->where('is_active', true)
            ->get()
            ->groupBy('supervisor_id');
    }

    /**
     * Lock a user account.
     */
    public function lockUser(User $user, string $reason, User $restrictedBy): void
    {
        UserRestriction::create([
            'user_id' => $user->id,
            'restriction_type' => 'manual_lock',
            'reason' => $reason,
            'restricted_by' => $restrictedBy->id,
            'is_active' => true,
        ]);
    }

    /**
     * Unlock a user account.
     */
    public function unlockUser(User $user, string $reason, User $liftedBy): void
    {
        UserRestriction::where('user_id', $user->id)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $liftedBy->id,
                'lifted_at' => now(),
                'lift_reason' => $reason,
            ]);
    }

    /**
     * Check if a user can be assigned as Superior.
     */
    public function canBeAssignedAsSuperior(User $user): bool
    {
        // Must be a Mujeeb and not already a Superior
        return $user->hasRole('mujeeb') && !$user->hasRole('Superior');
    }

    /**
     * Check if a user can be assigned as Assistant.
     */
    public function canBeAssignedAsAssistant(User $user): bool
    {
        // Must be a Mujeeb and not already assigned to another Superior
        if (!$user->hasRole('mujeeb')) {
            return false;
        }

        // Check if already assigned as assistant
        return !SupervisorAssistant::where('assistant_id', $user->id)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Get users by role.
     */
    public function getUsersByRole(string $roleName): \Illuminate\Database\Eloquent\Collection
    {
        return User::whereHas('roles', function ($query) use ($roleName) {
            $query->where('name', $roleName);
        })->get();
    }

    /**
     * Assign multiple roles to a user.
     */
    public function assignRoles(User $user, array $roleNames): void
    {
        // Get role IDs from names
        $roleIds = Role::whereIn('name', $roleNames)->pluck('id')->toArray();

        // Sync roles (this will remove old roles and add new ones)
        $user->roles()->sync($roleIds);
    }

    /**
     * Remove a specific role from a user.
     */
    public function removeRole(User $user, string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();

        if ($role) {
            $user->roles()->detach($role->id);
        }
    }

    /**
     * Add a role to a user without removing existing roles.
     */
    public function addRole(User $user, string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();

        if ($role && !$user->roles->contains($role->id)) {
            $user->roles()->attach($role->id);
        }
    }

    /**
     * Get all users who can participate in supervisor-assistant mapping.
     * This includes all users regardless of their current assignments.
     */
    public function getAllMappableUsers(): \Illuminate\Database\Eloquent\Collection
    {
        return User::whereHas('roles')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get users available for a specific supervisor (excluding the supervisor themselves).
     */
    public function getAvailableAssistantsForSupervisor(User $supervisor): \Illuminate\Database\Eloquent\Collection
    {
        return User::where('id', '!=', $supervisor->id)
            ->whereHas('roles')
            ->orderBy('name')
            ->get();
    }

    /**
     * Check if a user can be assigned as assistant to a specific supervisor.
     * This allows more flexible assignments.
     */
    public function canBeAssignedAsAssistantTo(User $user, User $supervisor): bool
    {
        // User cannot be assistant to themselves
        if ($user->id === $supervisor->id) {
            return false;
        }

        // Check if already assigned to this specific supervisor
        $existingAssignment = SupervisorAssistant::where('supervisor_id', $supervisor->id)
            ->where('assistant_id', $user->id)
            ->where('is_active', true)
            ->exists();

        return !$existingAssignment;
    }
}
