<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Copy existing data from supervisor_assistants to supervisor_assistant_mappings
        DB::statement("
            INSERT INTO supervisor_assistant_mappings 
            (supervisor_id, assistant_id, assigned_at, assigned_by, is_active, created_at, updated_at)
            SELECT 
                supervisor_id, 
                assistant_id, 
                assigned_at, 
                assigned_by, 
                is_active, 
                created_at, 
                updated_at
            FROM supervisor_assistants
            WHERE is_active = 1
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear the new table
        DB::table('supervisor_assistant_mappings')->truncate();
    }
};
