<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User;
use App\Models\Department;
use App\Models\SupervisorAssistant;
use App\Models\SupervisorAssistantMapping as SupervisorAssistantMappingModel;
use App\Models\DepartmentSupervisorAssistant;
use App\Services\RoleManagementService;
use App\Services\DepartmentTeamManagementService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class SupervisorAssistantMapping extends Component
{
    use WithPagination, AuthorizesRequests;

    public $showAssignModal = false;
    public $showViewModal = false;
    public $showCreateSuperiorModal = false;
    public $showDepartmentAssignModal = false;
    public $showEditDepartmentsModal = false;
    public $showQuickAddForm = false;
    
    public $selectedSupervisor = null;
    public $selectedAssistants = [];
    public $availableAssistants = [];
    public $eligibleSuperiors = [];
    public $newSuperiorId = '';
    public $newSuperiorAssistants = [];
    
    public $search = '';
    public $filterDepartment = 'all';
    public $departments = [];
    
    public $mappings = [];
    public $departmentTeamStructures = [];
    public $statistics = [];

    // Department assignment properties
    public $selectedUserForDepartment;
    public $selectedDepartment;
    public $departmentRoleType = 'supervisor';
    public $departmentSupervisorId;
    
    // Quick add properties
    public $quickAddDepartmentId = '';
    public $quickAddRoleType = 'supervisor';
    public $tempDepartmentId = null;

    protected $roleManagementService;
    protected $departmentTeamService;

    public function boot(RoleManagementService $roleManagementService, DepartmentTeamManagementService $departmentTeamService)
    {
        $this->roleManagementService = $roleManagementService;
        $this->departmentTeamService = $departmentTeamService;
    }

    public function mount()
    {
        $this->authorize('assign-supervisors');
        $this->loadData();
        $this->loadStatistics();
        $this->loadDepartmentTeamStructures();
    }

    public function render()
    {
        $filteredMappings = collect($this->mappings)
            ->when($this->search, function ($collection) {
                return $collection->filter(function ($mapping) {
                    return stripos($mapping['supervisor']->name, $this->search) !== false ||
                           $mapping['assistants']->contains(function ($assistant) {
                               return stripos($assistant->name, $this->search) !== false;
                           });
                });
            })
            ->when($this->filterDepartment !== 'all', function ($collection) {
                return $collection->filter(function ($mapping) {
                    return $mapping['supervisor']->departments->contains('id', $this->filterDepartment);
                });
            });

        return view('livewire.supervisor-assistant-mapping', [
            'mappings' => $filteredMappings,
            'departmentTeamStructures' => $this->departmentTeamStructures,
            'availableSupervisors' => $this->getAvailableSupervisorsForDepartment($this->selectedDepartment),
        ]);
    }

    public function loadData()
    {
        // Load supervisor-assistant mappings using the new table that allows multiple supervisors per assistant
        $this->mappings = SupervisorAssistantMappingModel::with([
                'supervisor.supervisorDepartments',
                'assistant'
            ])
            ->where('is_active', true)
            ->get()
            ->groupBy('supervisor_id')
            ->map(function ($group) {
                $supervisor = $group->first()->supervisor;
                $assistants = $group->pluck('assistant');

                // For each assistant, get only the departments where they work under this specific supervisor
                $assistantsWithDepartments = $assistants->map(function ($assistant) use ($supervisor) {
                    $departmentsUnderThisSupervisor = \App\Models\DepartmentSupervisorAssistant::active()
                        ->assistants()
                        ->where('user_id', $assistant->id)
                        ->where('supervisor_id', $supervisor->id)
                        ->with('department')
                        ->get()
                        ->pluck('department');

                    $assistant->departmentsUnderThisSupervisor = $departmentsUnderThisSupervisor;
                    return $assistant;
                });

                return [
                    'supervisor' => $supervisor,
                    'assistants' => $assistantsWithDepartments,
                    'assigned_at' => $group->first()->assigned_at,
                    'assigned_by' => $group->first()->assignedBy,
                ];
            })
            ->values()
            ->toArray();

        // Load available data for modals - now more inclusive
        $this->availableAssistants = $this->roleManagementService->getEligibleAssistants();
        $this->eligibleSuperiors = $this->roleManagementService->getEligibleSuperiors();

        // Load departments
        $this->departments = \App\Models\Department::active()->select('id', 'name')->orderBy('name')->get();
    }

    public function loadStatistics()
    {
        $totalSuperiors = User::whereHas('roles', function ($query) {
            $query->where('name', 'Superior');
        })->count();

        $activeMappings = SupervisorAssistantMappingModel::where('is_active', true)->count();
        $totalAssistants = SupervisorAssistantMappingModel::where('is_active', true)->distinct('assistant_id')->count();
        $unassignedAssistants = $this->availableAssistants->count();

        $this->statistics = [
            'total_superiors' => $totalSuperiors,
            'active_mappings' => $activeMappings,
            'total_assistants' => $totalAssistants,
            'unassigned_assistants' => $unassignedAssistants,
            'avg_assistants_per_superior' => $totalSuperiors > 0 ? round($totalAssistants / $totalSuperiors, 1) : 0,
        ];
    }

    public function openAssignModal($supervisorId)
    {
        $this->selectedSupervisor = User::findOrFail($supervisorId);
        $this->authorize('assignAssistants', $this->selectedSupervisor);
        
        // Get current assistants from the new mapping table
        $currentAssistants = SupervisorAssistantMappingModel::where('supervisor_id', $supervisorId)
            ->where('is_active', true)
            ->pluck('assistant_id')
            ->toArray();
        
        $this->selectedAssistants = $currentAssistants;
        
        // Load all available assistants for this supervisor (more inclusive)
        $this->availableAssistants = $this->roleManagementService->getAvailableAssistantsForSupervisor($this->selectedSupervisor);
        
        $this->showAssignModal = true;
    }

    public function openViewModal($supervisorId)
    {
        $this->selectedSupervisor = User::with(['assistants.assistantDepartments', 'supervisorDepartments'])->findOrFail($supervisorId);
        $this->showViewModal = true;
    }

    public function openCreateSuperiorModal()
    {
        $this->authorize('assign-supervisors');
        $this->resetCreateForm();
        $this->showCreateSuperiorModal = true;
    }

    public function assignAssistants()
    {
        $this->authorize('assignAssistants', $this->selectedSupervisor);
        
        $this->roleManagementService->assignAssistants(
            $this->selectedSupervisor,
            $this->selectedAssistants,
            auth()->user()
        );

        $this->loadData();
        $this->loadStatistics();
        $this->showAssignModal = false;
        
        session()->flash('message', 'Assistants assigned successfully.');
    }

    public function createSuperior()
    {
        $this->authorize('assign-supervisors');
        
        $this->validate([
            'newSuperiorId' => 'required|exists:users,id',
            'newSuperiorAssistants' => 'array',
            'newSuperiorAssistants.*' => 'exists:users,id',
        ]);

        $user = User::findOrFail($this->newSuperiorId);
        
        $this->roleManagementService->assignSuperiorRole(
            $user,
            $this->newSuperiorAssistants,
            auth()->user()
        );

        $this->loadData();
        $this->loadStatistics();
        $this->showCreateSuperiorModal = false;
        
        session()->flash('message', "Superior role assigned to {$user->name} successfully.");
    }

    public function removeSuperior($supervisorId)
    {
        $supervisor = User::findOrFail($supervisorId);
        $this->authorize('assignRoles', $supervisor);
        
        $this->roleManagementService->removeSuperiorRole($supervisor);

        $this->loadData();
        $this->loadStatistics();
        
        session()->flash('message', "Superior role removed from {$supervisor->name}.");
    }

    public function removeAssistant($supervisorId, $assistantId)
    {
        $supervisor = User::findOrFail($supervisorId);
        $this->authorize('assignAssistants', $supervisor);
        
        // Remove from new mapping table
        SupervisorAssistantMappingModel::where('supervisor_id', $supervisorId)
            ->where('assistant_id', $assistantId)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        // Also remove from old table for backward compatibility
        SupervisorAssistant::where('supervisor_id', $supervisorId)
            ->where('assistant_id', $assistantId)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        $this->loadData();
        $this->loadStatistics();
        
        $assistant = User::findOrFail($assistantId);
        session()->flash('message', "Removed {$assistant->name} from {$supervisor->name}'s team.");
    }

    public function closeModals()
    {
        $this->showAssignModal = false;
        $this->showViewModal = false;
        $this->showCreateSuperiorModal = false;
        $this->showEditDepartmentsModal = false;
        $this->showQuickAddForm = false;
        $this->selectedSupervisor = null;
        $this->resetCreateForm();
    }

    private function resetCreateForm()
    {
        $this->newSuperiorId = '';
        $this->newSuperiorAssistants = [];
        $this->resetValidation();
    }

    public function updatedSearch()
    {
        // No pagination reset needed since we're using collection filtering
    }

    public function updatedFilterDepartment()
    {
        // No pagination reset needed since we're using collection filtering
    }

    public function refreshData()
    {
        $this->loadData();
        $this->loadStatistics();
        session()->flash('message', 'Data refreshed successfully.');
    }

    public function exportMappings()
    {
        $this->authorize('generate-reports');
        
        // This would generate a PDF or Excel report
        session()->flash('message', 'Mapping export functionality will be implemented.');
    }

    public function getDepartmentBadgeColor($departmentName)
    {
        $colors = [
            'Research' => 'primary',
            'Documentation' => 'info',
            'Quality Control' => 'success',
            'Translation' => 'warning',
            'Digital Media' => 'secondary',
            'Administration' => 'dark',
        ];

        return $colors[$departmentName] ?? 'secondary';
    }

    /**
     * Load department team structures.
     */
    public function loadDepartmentTeamStructures()
    {
        $departments = Department::active()->get();
        $this->departmentTeamStructures = [];

        foreach ($departments as $department) {
            $this->departmentTeamStructures[$department->id] = [
                'department' => $department,
                'structure' => $this->departmentTeamService->getDepartmentTeamStructure($department),
            ];
        }
    }

    /**
     * Open department assignment modal.
     */
    public function openDepartmentAssignModal($userId)
    {
        $this->authorize('assign-supervisors');
        $this->selectedUserForDepartment = User::findOrFail($userId);
        $this->selectedDepartment = '';
        $this->departmentRoleType = 'supervisor';
        $this->departmentSupervisorId = '';
        $this->showDepartmentAssignModal = true;
    }

    /**
     * Assign user to department with role.
     */
    public function assignToDepartment()
    {
        $this->authorize('assign-supervisors');

        $this->validate([
            'selectedDepartment' => 'required|exists:departments,id',
            'departmentRoleType' => 'required|in:supervisor,assistant',
            'departmentSupervisorId' => 'required_if:departmentRoleType,assistant|exists:users,id',
        ]);

        try {
            $department = Department::findOrFail($this->selectedDepartment);
            $user = $this->selectedUserForDepartment;

            if ($this->departmentRoleType === 'supervisor') {
                $this->departmentTeamService->assignSupervisor($department, $user, auth()->user());
                $message = "User {$user->name} assigned as supervisor to {$department->name}.";
            } else {
                $supervisor = User::findOrFail($this->departmentSupervisorId);
                $this->departmentTeamService->assignAssistant($department, $user, $supervisor, auth()->user());
                $message = "User {$user->name} assigned as assistant to {$supervisor->name} in {$department->name}.";
            }

            $this->loadDepartmentTeamStructures();
            $this->loadStatistics();
            $this->showDepartmentAssignModal = false;

            session()->flash('message', $message);
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        }
    }

    /**
     * Remove user from department.
     */
    public function removeFromDepartment($departmentId, $userId)
    {
        $this->authorize('assign-supervisors');

        try {
            $department = Department::findOrFail($departmentId);
            $user = User::findOrFail($userId);

            $this->departmentTeamService->removeUserFromDepartment($department, $user);

            $this->loadDepartmentTeamStructures();
            $this->loadStatistics();

            session()->flash('message', "User {$user->name} removed from {$department->name}.");
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        }
    }

    /**
     * Get available supervisors for a department.
     */
    public function getAvailableSupervisorsForDepartment($departmentId)
    {
        if (!$departmentId) return collect();

        return DepartmentSupervisorAssistant::active()
            ->supervisors()
            ->forDepartment($departmentId)
            ->with('user')
            ->get()
            ->pluck('user');
    }

    /**
     * Close department assignment modal.
     */
    public function closeDepartmentAssignModal()
    {
        $this->showDepartmentAssignModal = false;
        $this->selectedUserForDepartment = null;
        $this->selectedDepartment = '';
        $this->departmentRoleType = 'supervisor';
        $this->departmentSupervisorId = '';
    }

    /**
     * Remove user from a specific department.
     */
    public function removeUserFromDepartment($userId, $departmentId)
    {
        $this->authorize('assign-supervisors');

        try {
            $user = User::findOrFail($userId);
            $department = Department::findOrFail($departmentId);

            // Remove from user_departments table (old system)
            \DB::table('user_departments')
                ->where('user_id', $userId)
                ->where('department_id', $departmentId)
                ->delete();

            // Remove from department_supervisor_assistants table (new system)
            DepartmentSupervisorAssistant::where('user_id', $userId)
                ->where('department_id', $departmentId)
                ->update(['is_active' => false]);

            // Refresh the selected user data for the modal
            $this->selectedUserForDepartment = User::with(['supervisorDepartments', 'assistantDepartments'])->findOrFail($userId);
            
            // Refresh other data
            $this->loadData();
            $this->loadDepartmentTeamStructures();
            $this->loadStatistics();

            session()->flash('message', "Removed {$user->name} from {$department->name} department.");
        } catch (\Exception $e) {
            session()->flash('error', 'Error removing user from department: ' . $e->getMessage());
        }
    }

    /**
     * Open modal to edit user's department assignments.
     */
    public function openEditDepartmentsModal($userId)
    {
        $this->authorize('assign-supervisors');
        $this->selectedUserForDepartment = User::with(['supervisorDepartments', 'assistantDepartments'])->findOrFail($userId);
        $this->showEditDepartmentsModal = true;
    }

    /**
     * Close edit departments modal.
     */
    public function closeEditDepartmentsModal()
    {
        $this->showEditDepartmentsModal = false;
        $this->showQuickAddForm = false;
        $this->selectedUserForDepartment = null;
        $this->quickAddDepartmentId = '';
        $this->quickAddRoleType = 'supervisor';
    }

    /**
     * Show quick add form in edit departments modal.
     */
    public function showQuickAddForm()
    {
        $this->showQuickAddForm = true;
        $this->quickAddDepartmentId = '';
        $this->quickAddRoleType = 'supervisor';
    }

    /**
     * Hide quick add form.
     */
    public function hideQuickAddForm()
    {
        $this->showQuickAddForm = false;
        $this->quickAddDepartmentId = '';
        $this->quickAddRoleType = 'supervisor';
    }

    /**
     * Process quick add from the form.
     */
    public function processQuickAdd()
    {
        $this->validate([
            'quickAddDepartmentId' => 'required|exists:departments,id',
            'quickAddRoleType' => 'required|in:supervisor,assistant',
        ]);

        if ($this->quickAddRoleType === 'supervisor') {
            $this->quickAddToDepartment($this->selectedUserForDepartment->id, $this->quickAddDepartmentId);
        } else {
            // For assistant role, we need to open the full assignment modal
            $this->selectedDepartment = $this->quickAddDepartmentId;
            $this->departmentRoleType = 'assistant';
            $this->showEditDepartmentsModal = false;
            $this->showDepartmentAssignModal = true;
        }

        $this->hideQuickAddForm();
    }

    /**
     * Test method to check if Livewire is working.
     */
    public function testLivewire()
    {
        session()->flash('message', 'Livewire is working! Test successful.');
        \Log::info('Test Livewire method called successfully');
    }

    /**
     * Add department to the currently selected user (simpler method).
     */
    public function addDepartmentToUser($departmentId)
    {
        if (!$this->selectedUserForDepartment) {
            session()->flash('error', 'No user selected.');
            return;
        }

        $this->quickAddToDepartment($this->selectedUserForDepartment->id, $departmentId);
    }

    /**
     * Set department ID and add (no parameters method).
     */
    public function setAndAddDepartment()
    {
        \Log::info("setAndAddDepartment called with tempDepartmentId: {$this->tempDepartmentId}");
        
        if (!$this->tempDepartmentId || !$this->selectedUserForDepartment) {
            session()->flash('error', 'Invalid department or user selection. TempID: ' . $this->tempDepartmentId);
            return;
        }

        $this->quickAddToDepartment($this->selectedUserForDepartment->id, $this->tempDepartmentId);
        $this->tempDepartmentId = null;
    }

    /**
     * Add department by string ID (simpler approach).
     */
    public function addDepartmentById($departmentId)
    {
        \Log::info("addDepartmentById called with departmentId: {$departmentId}");
        
        if (!$this->selectedUserForDepartment) {
            session()->flash('error', 'No user selected.');
            return;
        }

        $this->quickAddToDepartment($this->selectedUserForDepartment->id, (int)$departmentId);
    }

    /**
     * Quick add user to department as supervisor.
     */
    public function quickAddToDepartment($userId, $departmentId)
    {
        $this->authorize('assign-supervisors');

        try {
            $user = User::findOrFail($userId);
            $department = Department::findOrFail($departmentId);

            // Check if already assigned and active
            $existingActiveAssignment = DepartmentSupervisorAssistant::where('user_id', $userId)
                ->where('department_id', $departmentId)
                ->where('is_active', true)
                ->first();

            if ($existingActiveAssignment) {
                session()->flash('error', "{$user->name} is already assigned to {$department->name}.");
                return;
            }

            // Add to old system - first check if already exists
            $existsInOldSystem = \DB::table('user_departments')
                ->where('user_id', $userId)
                ->where('department_id', $departmentId)
                ->exists();
                
            if (!$existsInOldSystem) {
                \DB::table('user_departments')->insert([
                    'user_id' => $userId,
                    'department_id' => $departmentId,
                    'assigned_at' => now(),
                    'assigned_by' => auth()->id(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Add to new system as supervisor by default
            // First check if there's an inactive record we can reactivate
            $inactiveAssignment = DepartmentSupervisorAssistant::where('user_id', $userId)
                ->where('department_id', $departmentId)
                ->where('is_active', false)
                ->first();

            if ($inactiveAssignment) {
                // Reactivate existing record
                $inactiveAssignment->update([
                    'is_active' => true,
                    'assigned_by' => auth()->id(),
                    'updated_at' => now(),
                ]);
            } else {
                // Create new record
                DepartmentSupervisorAssistant::create([
                    'user_id' => $userId,
                    'department_id' => $departmentId,
                    'role_type' => 'supervisor',
                    'assigned_by' => auth()->id(),
                    'is_active' => true,
                ]);
            }

            // Refresh the selected user data
            $this->selectedUserForDepartment = User::with(['supervisorDepartments', 'assistantDepartments'])->findOrFail($userId);
            
            // Refresh other data
            $this->loadData();
            $this->loadDepartmentTeamStructures();
            $this->loadStatistics();

            session()->flash('message', "Added {$user->name} to {$department->name} as supervisor.");
            
            // Log for debugging
            \Log::info("QuickAddToDepartment called: User {$userId}, Department {$departmentId}");
            
        } catch (\Exception $e) {
            session()->flash('error', 'Error adding user to department: ' . $e->getMessage());
        }
    }
}
