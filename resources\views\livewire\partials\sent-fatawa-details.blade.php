<!-- Detailed Fatawa Section -->
<div class="modern-card" x-data="{ allExpanded: false }">
    <div class="modern-card-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    Sent Fatawa Details
                </h5>
                <p class="mb-0 opacity-75">Detailed view of all sent fatawa with download and management options</p>
            </div>
            <button @click="allExpanded = !allExpanded" class="btn-modern btn-outline-modern">
                <i class="fas" :class="allExpanded ? 'fa-compress' : 'fa-expand'" class="me-2"></i>
                <span x-text="allExpanded ? 'Collapse All' : 'Expand All'"></span>
            </button>
        </div>
    </div>
    <div class="modern-card-body">
        @foreach($daruliftaNames as $daruliftaName)
            @if(isset($remainingFatawa[$daruliftaName]))
                <div class="toggle-section" x-data="{ expanded: true }" x-init="$watch('allExpanded', value => expanded = value)">
                    <div class="toggle-header-modern" @click="expanded = !expanded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-building me-2"></i>
                            <span class="fw-bold">{{ $daruliftaName }}</span>
                        </div>
                        <i class="fas" :class="expanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                    </div>

                    <div x-show="expanded" x-transition class="toggle-content-modern">
                        @php
                            $serialNumber_fl = 1;
                            $colors = ['#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1'];
                            $isAdmin = in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                        @endphp

                        @foreach($mailfolderDate as $mailfolderDates)
                            @if(isset($remainingFatawa[$daruliftaName][$mailfolderDates]))
                                @php
                                    $fatawaCollection = $remainingFatawa[$daruliftaName][$mailfolderDates];
                                    $mailfolderDateCount = is_array($fatawaCollection) ? count($fatawaCollection) : $fatawaCollection->count();
                                    $formattedDate = (new DateTime($mailfolderDates))->format('d-m-Y');
                                @endphp

                                <div class="modern-card mb-3" style="background-color: {{ $colors[$loop->index % count($colors)] }};">
                                    <div x-data="{ folderExpanded: {{ $showQue ? 'true' : 'false' }} }" x-init="$watch('allExpanded', value => folderExpanded = value)">
                                        <div class="modern-card-header cursor-pointer" @click="folderExpanded = !folderExpanded"
                                             style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0 text-white">
                                                        <span class="badge bg-white text-primary me-2">{{ $serialNumber_fl++ }}</span>
                                                        <i class="fas fa-calendar-alt me-2"></i>
                                                        {{ $formattedDate }}
                                                    </h6>
                                                    <small class="text-white opacity-75">
                                                        <i class="fas fa-file-alt me-1"></i>
                                                        {{ $mailfolderDateCount }} Fatawa
                                                    </small>
                                                </div>
                                                <div class="d-flex align-items-center gap-3">
                                                    <!-- Download and Delete Actions -->
                                                    @php
                                                        $fatawaCollectionSafe = is_array($fatawaCollection) ? collect($fatawaCollection) : $fatawaCollection;
                                                        $firstFatawa = $fatawaCollectionSafe->first();
                                                        $downloadedByAdmin = $firstFatawa->downloaded_by_admin ?? null;
                                                        $canDelete = is_null($downloadedByAdmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());

                                                        $sentFatawaCollection = is_array($fatawaCollection)
                                                            ? collect($fatawaCollection)
                                                            : $fatawaCollection;

                                                        $uniquePairs = $sentFatawaCollection
                                                            ->filter(function($file) use ($mailfolderDates) {
                                                                return $file->mail_folder_date == $mailfolderDates;
                                                            })
                                                            ->map(function($file) {
                                                                return [
                                                                    'checker' => $file->checker,
                                                                    'transfer_by' => !empty($file->transfer_by) ? $file->transfer_by : 'Mujeeb',
                                                                    'checkedFolder' => $file->checked_folder,
                                                                    'byMufti' => $file->by_mufti,
                                                                    'Id' => $file->id,
                                                                    'mail_folder_date' => $file->mail_folder_date
                                                                ];
                                                            })
                                                            ->unique(function ($item) {
                                                                return $item['mail_folder_date'] . '-' . $item['checker'] . '-' . $item['transfer_by'];
                                                            });
                                                    @endphp

                                                    <div class="d-flex gap-2" onclick="event.stopPropagation();">
                                                        @foreach($uniquePairs as $pair)
                                                            @php
                                                                $downloadUrl = route('downloadFolder', [
                                                                    'daruliftaName' => $daruliftaName,
                                                                    'mailfolderDates' => $mailfolderDates,
                                                                    'checker' => $pair['checker'] ?? '',
                                                                    'transferBy' => $pair['transfer_by'] ?? '',
                                                                    'checkedFolder' => $pair['checkedFolder'] ?? '',
                                                                    'byMufti' => $pair['byMufti'] ?? ''
                                                                ]);

                                                                $deleteUrl = route('deleteFolder', [
                                                                    'daruliftaName' => $daruliftaName,
                                                                    'mailfolderDates' => $mailfolderDates,
                                                                    'checker' => $pair['checker'] ?? '',
                                                                    'transferBy' => $pair['transfer_by'] ?? '',
                                                                    'byMufti' => $pair['byMufti'] ?? '',
                                                                    'checkedFolder' => $pair['checkedFolder'] ?? ''
                                                                ]);

                                                                $deleteFolderFormId = 'delete-folder-form-sent-' . $pair['Id'];
                                                                $isMujeeb = Auth::user()->isMujeeb();
                                                            @endphp

                                                            @if (!$isMujeeb)
                                                                <a href="{{ $downloadUrl }}" class="btn-modern btn-success-modern btn-sm"
                                                                   title="Download Folder {{ $pair['checker'] }} by {{ $pair['transfer_by'] }}">
                                                                    <i class="fas fa-download action-icon"></i>
                                                                </a>
                                                            @else
                                                                <button class="btn-modern btn-success-modern btn-sm opacity-50" disabled
                                                                        title="Download not allowed for Mujeeb role">
                                                                    <i class="fas fa-download action-icon"></i>
                                                                </button>
                                                            @endif

                                                            @if ($canDelete && !$isMujeeb)
                                                                <button onclick="if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('{{ $deleteFolderFormId }}').submit(); }"
                                                                        class="btn-modern btn-danger-modern btn-sm" title="Delete Folder">
                                                                    <i class="fas fa-trash action-icon"></i>
                                                                </button>
                                                                <form id="{{ $deleteFolderFormId }}" action="{{ $deleteUrl }}" method="POST" class="d-none">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                </form>
                                                            @else
                                                                @php
                                                                    $disableTitle = $isMujeeb
                                                                        ? "Delete not allowed for Mujeeb role"
                                                                        : "Cannot delete, downloaded by admin on {$downloadedByAdmin}";
                                                                @endphp
                                                                <button class="btn-modern btn-danger-modern btn-sm opacity-50" disabled
                                                                        title="{{ $disableTitle }}">
                                                                    <i class="fas fa-trash action-icon"></i>
                                                                </button>
                                                            @endif
                                                        @endforeach
                                                    </div>

                                                    <i class="fas text-white" :class="folderExpanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Detailed Table View -->
                                        <div x-show="folderExpanded" x-transition class="modern-card-body">
                                            @if ($showDetail)
                                                <div class="table-responsive">
                                                    <table class="table-modern">
                                                        <thead>
                                                            <tr>
                                                                <th class="text-center" style="width: 5%;">
                                                                    <i class="fas fa-hashtag me-1"></i>
                                                                    S.No
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-file-code me-1"></i>
                                                                    Fatwa No
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-user me-1"></i>
                                                                    Mujeeb
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-tag me-1"></i>
                                                                    Type
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-folder me-1"></i>
                                                                    Category
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-user-check me-1"></i>
                                                                    Checker
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-calendar me-1"></i>
                                                                    Send Date
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-clock me-1"></i>
                                                                    Reception Info
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-folder-open me-1"></i>
                                                                    M.Folder
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-folder-check me-1"></i>
                                                                    Checked Folder
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-star me-1"></i>
                                                                    Checked Grade
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-clipboard-check me-1"></i>
                                                                    Checked Tasurat
                                                                </th>
                                                                @if ($isAdmin)
                                                                    <th style="width: 9%;">
                                                                        <i class="fas fa-info-circle me-1"></i>
                                                                        Details
                                                                    </th>
                                                                @endif
                                                                <th class="text-center" style="width: 10%;">
                                                                    <i class="fas fa-cogs me-1"></i>
                                                                    Actions
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @php $serialNumber_se = 1; @endphp
                                                            @foreach($sentFatawaCollection as $file)
                                                                <tr>
                                                                    <td class="text-center">
                                                                        <span class="badge bg-primary">{{ $serialNumber_se++ }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <div class="fw-bold text-primary">{{ $file->file_code }}</div>
                                                                        @if(isset($file->source) && $file->source === 'talaq')
                                                                            <span class="badge bg-warning text-dark mt-1">Talaq</span>
                                                                        @endif
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success fw-medium urdu-text">{{ $file->sender }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="badge bg-info text-white">{{ $file->ftype }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success urdu-text">{{ $file->category }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <div class="d-flex flex-column">
                                                                            <span class="fw-bold text-primary">{{ $file->checker }}</span>
                                                                            <small class="text-muted">
                                                                                by: {{ !empty($file->transfer_by) ? $file->transfer_by : 'Mujeeb' }}
                                                                            </small>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success">{{ $file->mail_recived_date }}</span>
                                                                    </td>
                                                                    <td>
                                                                        @foreach ($que_day_r as $day)
                                                                            @if (Str::lower($file->file_code) == Str::lower($day->ifta_code))
                                                                                @php
                                                                                    $recDate = new \DateTime($day->rec_date);
                                                                                    $currentDate = now();
                                                                                    $daysDifference = $currentDate->diff($recDate)->days;
                                                                                @endphp
                                                                                <div class="d-flex flex-column">
                                                                                    <span class="text-primary fw-bold">{{ $day->rec_date }}</span>
                                                                                    <span class="badge bg-success">{{ $daysDifference }} days</span>
                                                                                </div>
                                                                            @endif
                                                                        @endforeach
                                                                    </td>
                                                                    <td>
                                                                        @foreach ($mahlenazar_null as $mahle)
                                                                            @if ($file->file_code == $mahle->file_code && $mahle->mail_folder_date < $file->mail_folder_date)
                                                                                <span class="text-warning">{{ $mahle->mail_folder_date }}</span>
                                                                            @endif
                                                                        @endforeach
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-info fw-medium">{{ $file->checked_folder ?? 'N/A' }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="badge bg-warning text-dark">{{ $file->checked_grade ?? 'N/A' }}</span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success urdu-text">{{ $file->checked_tasurat ?? 'N/A' }}</span>
                                                                    </td>
                                                                    @if ($isAdmin)
                                                                        <td>
                                                                            <a href="{{ route('fatwa-detail', ['fatwa' => $file->file_code]) }}"
                                                                               target="_blank" class="btn-modern btn-info-modern btn-sm" title="View Details">
                                                                                <i class="fas fa-info-circle action-icon action-details"></i>
                                                                            </a>
                                                                        </td>
                                                                    @endif
                                                                    <td>
                                                                        <span class="view">
                                                                            @php
                                                                                // Determine the date parameter based on conditions
                                                                                if (empty($file->by_mufti)) {
                                                                                    if (empty($file->checker)) {
                                                                                        $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked';
                                                                                    } else {
                                                                                        $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked_by_' . $file->checker;
                                                                                    }
                                                                                } else {
                                                                                    $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked_by_' . $file->checker . '_' . $file->by_mufti;
                                                                                }
                                                                            @endphp

                                                                            <a href="{{ route('viewCheck', [
                                                                                'date' => $dateParam,
                                                                                'folder' => $file->checked_folder,
                                                                                'filename' => $file->file_name
                                                                            ]) }}" target="_blank">
                                                                                <i class="fas fa-eye"></i>
                                                                            </a>
                                                                        </span>

                                                                        <span class="download">
                                                                            <a href="{{ route('downloadCheck', [
                                                                                'date' => $dateParam,
                                                                                'filename' => $file->file_name,
                                                                                'folder' => $file->checked_folder
                                                                            ]) }}">
                                                                                <i class="fas fa-download"></i>
                                                                            </a>
                                                                        </span>

                                                                        @php
                                                                            $deleteFile = route('deleteCheckedFile', [
                                                                                'mailfolderDates' => $file->mail_folder_date,
                                                                                'daruliftaName' => $file->darulifta_name,
                                                                                'checker' => $file->checker ?? ''
                                                                            ]);

                                                                            $deleteFileFormId = 'delete-file-form-' . $file->id;
                                                                            $downloadfileByadmin = $file->downloaded_by_admin;
                                                                            $userRoles = Auth::user()->roles->pluck('name')->toArray();
                                                                            $isAdmin = in_array('Admin', $userRoles);
                                                                        @endphp

                                                                        <span class="delete">
                                                                            @if ($isAdmin)
                                                                                <a href="#" onclick="event.preventDefault(); if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('{{ $deleteFileFormId }}').submit(); }" class="text-danger">
                                                                                    <i class="fas fa-trash"></i>
                                                                                </a>
                                                                                <form id="{{ $deleteFileFormId }}" action="{{ $deleteFile }}?{{ $queryString }}" method="POST" class="d-none">
                                                                                    @csrf
                                                                                    @method('DELETE')
                                                                                    <input type="hidden" name="id" value="{{ $file->id }}">
                                                                                    <input type="hidden" name="file_name" value="{{ $file->file_name }}">
                                                                                    <input type="hidden" name="file_code" value="{{ $file->file_code }}">
                                                                                    <input type="hidden" name="checked_folder" value="{{ $file->checked_folder }}">
                                                                                </form>
                                                                            @else
                                                                                <span class="text-danger" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Cannot delete, only Admins can delete.">
                                                                                    <i class="fas fa-trash mx-1"></i>
                                                                                </span>
                                                                            @endif
                                                                        </span>
                                                                    </td>
                                                                </tr>

                                                                <!-- Question Toggle and Content -->
                                                                @if ($showQue)
                                                                    <tr x-data="{ openQuestion: true }">
                                                                        <td colspan="1" @click="openQuestion = !openQuestion" class="align-middle text-center cursor-pointer" style="background-color: #FFDDCC;">
                                                                            Question <span x-text="openQuestion ? '◀' : '▶'"></span>
                                                                        </td>
                                                                        <td x-show="openQuestion" @click.outside="openQuestion = false" colspan="{{ $isAdmin ? '10' : '9' }}" style="background-color: #ffffff;">
                                                                            <!-- Dynamic Content Here -->
                                                                            @foreach ($que_day_r as $day)
                                                                                @if (Str::lower($file->file_code) == Str::lower($day->ifta_code))
                                                                                    <div class="question-text">سوال: {{ $day->question }}</div>
                                                                                @endif
                                                                            @endforeach
                                                                        </td>
                                                                    </tr>
                                                                @endif

                                                                @if ($showChat)
                                                                    <!-- Chat Toggle and Content -->
                                                                    <tr x-data="{ openChat: true }">
                                                                        <td colspan="1" @click="openChat = !openChat" class="align-middle text-center cursor-pointer toggle-chat right-aligned" data-section="chat" style="background-color: #FFDDCC;">
                                                                            Chat <span x-text="openChat ? '◀' : '▶'"></span>
                                                                        </td>
                                                                        <td x-show="openChat" colspan="{{ $isAdmin ? '10' : '9' }}" class="align-middle text-center">
                                                                            <div class="d-flex justify-content-center align-items-center chat-container">
                                                                                <div class="col-md-6 col-lg-7 col-xl-8">
                                                                                    <ul class="list-unstyled">
                                                                                        @foreach ($messages->sortBy('created_at') as $message)
                                                                                            @if($message->ifta_code == $file->file_code)
                                                                                                @if ($message->user_id == auth()->user()->id)
                                                                                                    <li class="d-flex justify-content-between mb-4">
                                                                                                        <span class="badge rounded-circle bg-success d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                                                                                            {{ strtoupper(substr($message->user_name, 0, 1)) }}
                                                                                                        </span>
                                                                                                        <div class="card w-auto">
                                                                                                            <div class="card-header d-flex justify-content-between p-3">
                                                                                                                <p class="fw-bold mb-0">
                                                                                                                    {{ $message->user_name }}
                                                                                                                </p>
                                                                                                                <p class="text-muted small mb-0">
                                                                                                                    <i class="far fa-clock"></i> {{ $message->created_at }}
                                                                                                                </p>
                                                                                                            </div>
                                                                                                            <div class="card-body" style="background-color:#ADD8E6;">
                                                                                                                <p class="mb-0">
                                                                                                                    {{ $message->message }}
                                                                                                                </p>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </li>
                                                                                                @else
                                                                                                    <!-- Unauthenticated / Other users messages. -->
                                                                                                    <li class="d-flex justify-content-between mb-4">
                                                                                                        <div class="card w-auto">
                                                                                                            <div class="card-header d-flex justify-content-between p-3">
                                                                                                                <p class="fw-bold mb-0">
                                                                                                                    {{ $message->user_name }}
                                                                                                                </p>
                                                                                                                <p class="text-muted small mb-0">
                                                                                                                    <i class="far fa-clock"></i> {{ $message->created_at }}
                                                                                                                </p>
                                                                                                            </div>
                                                                                                            <div class="card-body">
                                                                                                                <p class="mb-0">
                                                                                                                    {{ $message->message }}
                                                                                                                </p>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <span class="badge rounded-circle bg-primary d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                                                                                            {{ strtoupper(substr($message->user_name, 0, 1)) }}
                                                                                                        </span>
                                                                                                    </li>
                                                                                                @endif
                                                                                            @endif
                                                                                        @endforeach

                                                                                        <div class="bg-light">
                                                                                            <div class="input-group">
                                                                                                <input wire:model="message" type="text" placeholder="Type a message" aria-describedby="button-addon2" class="form-control rounded-0 border-0 py-4 bg-light text-end">
                                                                                                <div class="input-group-append">
                                                                                                    <button id="button-addon2" class="btn btn-link" wire:click="sendMessage('{{ $file->file_code }}')"> <i class="fa fa-paper-plane"></i></button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                @endif
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            @else
                                                <div class="text-center py-4">
                                                    <i class="fas fa-info-circle text-muted me-2"></i>
                                                    <span class="text-muted">Enable "Load Fatawa Detail" to view detailed information</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            @endif
        @endforeach
    </div>
</div>
