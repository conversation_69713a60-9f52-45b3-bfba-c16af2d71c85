<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\DepartmentSupervisorAssistant;
use App\Models\User;

// Get Hasnain's user
$hasnain = User::where('name', 'like', '%Hasnain%')->first();
if (!$hasnain) {
    $hasnain = User::find(57); // fallback to ID 57
}

if (!$hasnain) {
    echo "Hasnain not found\n";
    exit;
}

echo "Hasnain ID: {$hasnain->id}, Name: {$hasnain->name}\n";

// Get Farhan and Sayed
$farhan = User::where('name', 'like', '%Farhan%')->first();
$sayed = User::where('name', 'like', '%Sayed%')->first();

echo "Farhan ID: " . ($farhan ? $farhan->id : 'Not found') . "\n";
echo "Sayed ID: " . ($sayed ? $sayed->id : 'Not found') . "\n";

// Check department assignments for Hasnain
$assignments = DepartmentSupervisorAssistant::active()
    ->assistants()
    ->where('user_id', $hasnain->id)
    ->with(['department', 'supervisor'])
    ->get();

echo "\nHasnain's department assignments:\n";
foreach ($assignments as $assignment) {
    echo "- Department: {$assignment->department->name}, Supervisor: {$assignment->supervisor->name} (ID: {$assignment->supervisor->id})\n";
}

// Check what departments Hasnain would show under Farhan
if ($farhan) {
    $departmentsUnderFarhan = DepartmentSupervisorAssistant::active()
        ->assistants()
        ->where('user_id', $hasnain->id)
        ->where('supervisor_id', $farhan->id)
        ->with('department')
        ->get();
    
    echo "\nDepartments where Hasnain works under Farhan:\n";
    foreach ($departmentsUnderFarhan as $assignment) {
        echo "- {$assignment->department->name}\n";
    }
    if ($departmentsUnderFarhan->isEmpty()) {
        echo "- None\n";
    }
}

// Check what departments Hasnain would show under Sayed
if ($sayed) {
    $departmentsUnderSayed = DepartmentSupervisorAssistant::active()
        ->assistants()
        ->where('user_id', $hasnain->id)
        ->where('supervisor_id', $sayed->id)
        ->with('department')
        ->get();
    
    echo "\nDepartments where Hasnain works under Sayed:\n";
    foreach ($departmentsUnderSayed as $assignment) {
        echo "- {$assignment->department->name}\n";
    }
    if ($departmentsUnderSayed->isEmpty()) {
        echo "- None\n";
    }
}
