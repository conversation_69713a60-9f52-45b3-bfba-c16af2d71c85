<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{



    public function index(Request $request, $role = null, $fatwa_no = null)
    {
        $user = Auth::user();
        $userName = Auth::user()->name;
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        
        // Get the first day of the current month
        $firstDayOfMonth = Carbon::now()->startOfMonth();
        // Get the last day of the current month
        $lastDayOfMonth = Carbon::now()->endOfMonth();

        // Determine user role and get appropriate data
        $roleInfo = $this->determineUserRole();
        
        // Get darulifta names based on role
        if ($roleInfo['isMujeeb']) {
            // Get all departments for this mujeeb
            $daruliftaNames = DB::table('mujeebs')
                ->where('mujeeb_name', $userName)
                ->pluck('darul_name')
                ->toArray();
        } else if ($role == null) {
            if (count($userRoles) > 1) {
                $daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->pluck('darulifta_name');
            } else {
                $daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', reset($roleNames))
                    ->pluck('darulifta_name');
            }
        } else {
            $daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name', $role)
                ->pluck('darulifta_name');
        }

        // Get mail folder dates
        $mailfolderDates = DB::table('uploaded_files')
            ->select('mail_folder_date')
            ->where('selected', 0)
            ->distinct()
            ->orderBy('mail_folder_date', 'desc')
            ->pluck('mail_folder_date');

        // Get data by darulifta name and mail folder date
        $dataByDaruliftaName = [];
        foreach ($daruliftaNames as $daruliftaName) {
            foreach ($mailfolderDates as $mailfolderDate) {
                // Get data from uploaded_files
                $uploadedFilesQuery = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('selected', 0)
                    ->select('file_code', 'sender', 'ftype', 'mail_recived_date', 'category', DB::raw("'regular' as source"));

                // Get data from talaq_fatawa_manage
                $talaqFilesQuery = DB::table('talaq_fatawa_manage')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('selected', 0)
                    ->select('file_code', 'sender', 'ftype', 'mail_recived_date', 'category', DB::raw("'talaq' as source"));

                // Apply role filters
                $uploadedFilesQuery = $this->applyRoleFilters($uploadedFilesQuery, $roleInfo);
                $talaqFilesQuery = $this->applyRoleFilters($talaqFilesQuery, $roleInfo);

                // Combine results
                $combinedData = $uploadedFilesQuery->union($talaqFilesQuery)->get();

                if ($combinedData->isNotEmpty()) {
                    $dataByDaruliftaName[$daruliftaName][$mailfolderDate] = $combinedData;
                }
            }
        }

        // Generate summary report
        $summaryReport = [];
        foreach ($daruliftaNames as $daruliftaName) {
            // Get unique mail_folder_date values from uploaded_files
            $mailFolderDatesQuery1 = DB::table('uploaded_files')
                ->select('mail_folder_date')
                ->orderBy('mail_folder_date')
                ->where('selected', 0)
                ->where('darulifta_name', $daruliftaName)
                ->distinct();

            // Get unique mail_folder_date values from talaq_fatawa_manage
            $mailFolderDatesQuery2 = DB::table('talaq_fatawa_manage')
                ->select('mail_folder_date')
                ->orderBy('mail_folder_date')
                ->where('selected', 0)
                ->where('darulifta_name', $daruliftaName)
                ->distinct();

            // Apply role filters
            $mailFolderDatesQuery1 = $this->applyRoleFilters($mailFolderDatesQuery1, $roleInfo);
            $mailFolderDatesQuery2 = $this->applyRoleFilters($mailFolderDatesQuery2, $roleInfo);

            $mailFolderDates1 = $mailFolderDatesQuery1->pluck('mail_folder_date');
            $mailFolderDates2 = $mailFolderDatesQuery2->pluck('mail_folder_date');

            // Combine and sort unique dates
            $allDates = $mailFolderDates1->concat($mailFolderDates2)->unique()->sort()->values();

            $summaryItem = [
                'Darulifta' => $daruliftaName,
                'Mail Recived' => $allDates->implode('  |  ')
            ];

            $summaryReport[] = $summaryItem;
        }

        // Get remaining folder dates
        $remainfolderDateQuery = DB::table('uploaded_files')
            ->distinct()
            ->where('selected', 0)
            ->whereIn('darulifta_name', $daruliftaNames)
            ->where('darulifta_name', 'NOT LIKE', '%3btn%');

        $remainfolderDateQuery = $this->applyRoleFilters($remainfolderDateQuery, $roleInfo);
        $remainfolderDate = $remainfolderDateQuery->pluck(DB::raw("CONCAT(darulifta_name, ' - ', mail_folder_date) as merged_value"));

        // Get unselected files
        $unselectedFilesQuery = DB::table('uploaded_files')
            ->whereIn('darulifta_name', $daruliftaNames)
            ->where('selected', 0);
        $unselectedFilesQuery = $this->applyRoleFilters($unselectedFilesQuery, $roleInfo);
        $unselectedFiles = $unselectedFilesQuery->get();

        // Get unselected talaq
        $unselectedTalaqQuery = DB::table('talaq_fatawa_manage')
            ->whereIn('darulifta_name', $daruliftaNames)
            ->where('selected', 0);
        $unselectedTalaqQuery = $this->applyRoleFilters($unselectedTalaqQuery, $roleInfo);
        $unselectedTalaq = $unselectedTalaqQuery->count();

        // Get total questions count
        $totalQuestionsCountQuery = DB::table('questions')
            ->whereIn('question_branch', $daruliftaNames);
        $totalQuestionsCountQuery = $this->applyRoleFilters($totalQuestionsCountQuery, $roleInfo);
        $totalQuestionsCount = $totalQuestionsCountQuery->count();

        // Get monthly questions count
        $monthlyQuestionsCountQuery = DB::table('questions')
            ->whereIn('question_branch', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ])
            ->whereNotIn('ifta_code', function ($query) {
                $query->select('file_code')
                    ->from('uploaded_files')
                    ->where('checked_folder', '=', 'ok');
            });
        $monthlyQuestionsCountQuery = $this->applyRoleFilters($monthlyQuestionsCountQuery, $roleInfo);
        $monthlyQuestionsCount = $monthlyQuestionsCountQuery->count();

        // Get send to mufti count
        $sendToMuftiQuery = DB::table('questions')
            ->select('ifta_code')
            ->whereIn('question_branch', $daruliftaNames)
            ->whereIn('ifta_code', function ($subquery) {
                $subquery->select(DB::raw('LOWER(file_code)'))
                    ->from('uploaded_files');
            });
        $sendToMuftiQuery = $this->applyRoleFilters($sendToMuftiQuery, $roleInfo);
        $sendToMufti = $sendToMuftiQuery->count();

        // Get monthly send to mufti count
        $monthlysendToMuftiQuery = DB::table('questions')
            ->select('ifta_code')
            ->whereIn('question_branch', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ])
            ->whereIn('ifta_code', function ($subquery) {
                $subquery->select(DB::raw('LOWER(file_code)'))
                    ->from('uploaded_files');
            });
        $monthlysendToMuftiQuery = $this->applyRoleFilters($monthlysendToMuftiQuery, $roleInfo);
        $monthlysendToMufti = $monthlysendToMuftiQuery->count();

        // Get current month received count
        $currentmonthrecivedQuery = DB::table('uploaded_files')
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $currentmonthrecivedQuery = $this->applyRoleFilters($currentmonthrecivedQuery, $roleInfo);
        $currentmonthrecived = $currentmonthrecivedQuery->count();

        // Get current month checked count
        $currentmonthcheckedQuery = DB::table('uploaded_files')
            ->where('selected', 1)
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $currentmonthcheckedQuery = $this->applyRoleFilters($currentmonthcheckedQuery, $roleInfo);
        $currentmonthchecked = $currentmonthcheckedQuery->count();

        // Get current month talaq checked count
        $currentmonthTalaqcheckedQuery = DB::table('talaq_fatawa_manage')
            ->where('selected', 1)
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $currentmonthTalaqcheckedQuery = $this->applyRoleFilters($currentmonthTalaqcheckedQuery, $roleInfo);
        $currentmonthTalaqchecked = $currentmonthTalaqcheckedQuery->count();

        // Get today's checked count
        $today = now()->toDateString();
        $todayCheckedQuery = DB::table('uploaded_files')
            ->where('selected', 1)
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereDate('checked_date', $today);
        $todayCheckedQuery = $this->applyRoleFilters($todayCheckedQuery, $roleInfo);
        $todayChecked = $todayCheckedQuery->count();

        // Get viral stats
        $totalselectedviralQuery = DB::table('uploaded_files')
            ->where('viral', '!=', 0)
            ->whereIn('darulifta_name', $daruliftaNames);
        $totalselectedviralQuery = $this->applyRoleFilters($totalselectedviralQuery, $roleInfo);
        $totalselectedviral = $totalselectedviralQuery->count();

        $totalsentsviralQuery = DB::table('uploaded_files')
            ->whereNotNull('shoba_viral')
            ->whereIn('darulifta_name', $daruliftaNames);
        $totalsentsviralQuery = $this->applyRoleFilters($totalsentsviralQuery, $roleInfo);
        $totalsentsviral = $totalsentsviralQuery->count();

        $totalviralQuery = DB::table('uploaded_files')
            ->whereNotNull('viral_link')
            ->whereIn('darulifta_name', $daruliftaNames);
        $totalviralQuery = $this->applyRoleFilters($totalviralQuery, $roleInfo);
        $totalviral = $totalviralQuery->count();

        $totalwuploadQuery = DB::table('uploaded_files')
            ->whereNotNull('web_link')
            ->whereIn('darulifta_name', $daruliftaNames);
        $totalwuploadQuery = $this->applyRoleFilters($totalwuploadQuery, $roleInfo);
        $totalwupload = $totalwuploadQuery->count();

        // Get checked folder dates
        $checkedfolderDateQuery = DB::table('uploaded_files')
            ->distinct()
            ->whereIn('darulifta_name', $daruliftaNames)
            ->where('selected', 1)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $checkedfolderDateQuery = $this->applyRoleFilters($checkedfolderDateQuery, $roleInfo);
        $checkedfolderDate = $checkedfolderDateQuery->pluck(DB::raw("CONCAT(darulifta_name, ' - ', mail_folder_date) as merged_value"));

        // Get received folder dates
        $recivedfolderDateQuery = DB::table('uploaded_files')
            ->distinct()
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $recivedfolderDateQuery = $this->applyRoleFilters($recivedfolderDateQuery, $roleInfo);
        $recivedfolderDate = $recivedfolderDateQuery->pluck(DB::raw("CONCAT(darulifta_name, ' - ', mail_folder_date) as merged_value"));

        // Get total mahl-e-nazar count
        $totalmahlenazarQuery = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $totalmahlenazarQuery = $this->applyRoleFilters($totalmahlenazarQuery, $roleInfo);
        $totalmahlenazar = $totalmahlenazarQuery->count();

        // Get total ok fatawa count
        $totalokfatawaQuery = DB::table('uploaded_files')
            ->where('checked_folder', 'ok')
            ->whereIn('darulifta_name', $daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth]);
        $totalokfatawaQuery = $this->applyRoleFilters($totalokfatawaQuery, $roleInfo);
        $totalokfatawa = $totalokfatawaQuery->count();

        // Get total not sent count
        $totalnotsentQuery = DB::table('questions')
            ->whereIn('question_branch', $daruliftaNames)
            ->whereNotIn('ifta_code', function ($subquery) {
                $subquery->select(DB::raw('LOWER(file_code)'))
                    ->from('uploaded_files');
            });
        $totalnotsentQuery = $this->applyRoleFilters($totalnotsentQuery, $roleInfo);
        $totalnotsent = $totalnotsentQuery->count();

        // Get net mahl-e-nazar count (regular fatawa) - excluding transferred fatawa
        // First, get a list of all file codes that are currently assigned
        $assignedFileCodes = DB::table('uploaded_files')
            ->where('checked_folder', 'Assigned')
            ->select('file_code')
            ->distinct()
            ->pluck('file_code')
            ->toArray();

        // For each assigned file code, get the latest sender
        $currentAssignees = [];
        foreach ($assignedFileCodes as $fileCode) {
            $latestAssignment = DB::table('uploaded_files')
                ->where('file_code', $fileCode)
                ->where('checked_folder', 'Assigned')
                ->orderBy('id', 'desc')
                ->first();

            if ($latestAssignment) {
                $currentAssignees[$fileCode] = $latestAssignment->sender;
            }
        }

        // Get latest Mahl-e-Nazar entry for each file code FIRST (same as service logic)
        $latestIds = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        $uniquemahleQuery = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->where('u1.selected', 1)
            ->whereIn('u1.id', $latestIds) // Apply latest IDs filter first
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.file_code', 'u1.sender', DB::raw('MAX(u1.id) AS id'))
            ->groupBy('u1.file_code', 'u1.sender');

        // Apply role filters
        $uniquemahleQuery = $this->applyRoleFilters($uniquemahleQuery, $roleInfo);
        
        // Apply department filters if needed
        if (!empty($daruliftaNames)) {
            $uniquemahleQuery->whereIn('u1.darulifta_name', $daruliftaNames);
        }

        $uniquemahle = $uniquemahleQuery->get();

        // Filter out transferred fatawa (same logic as MahlenazarFatawa component)
        $filteredMahle = $uniquemahle->filter(function ($record) use ($currentAssignees, $roleInfo) {
            $fileCode = $record->file_code;
            $sender = $record->sender;

            // If this file code is assigned to someone else, exclude it
            if (isset($currentAssignees[$fileCode]) && $currentAssignees[$fileCode] !== $sender) {
                return false;
            }

            return true;
        });

        $regularMahlenazar = $filteredMahle->count();

        // Get net Talaq mahl-e-nazar count - excluding transferred fatawa
        // Get latest Talaq entries for each file code FIRST (same as service logic)
        $latestTalaqIds = DB::table('talaq_fatawa_manage')
            ->where('checked_folder', 'mahl-e-nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        $uniqueTalaqMahleQuery = DB::table('talaq_fatawa_manage as tfm1')
            ->where('tfm1.checked_folder', 'mahl-e-nazar')
            ->where('tfm1.selected', 1)
            ->whereIn('tfm1.id', $latestTalaqIds) // Apply latest IDs filter first
            ->leftJoin('talaq_fatawa_manage as tfm2', function ($join) {
                $join->on('tfm1.file_code', '=', 'tfm2.file_code')
                    ->where('tfm2.checked_folder', 'ok');
            })
            ->whereNull('tfm2.file_code')
            ->select('tfm1.file_code', 'tfm1.sender', DB::raw('MAX(tfm1.id) AS id'))
            ->groupBy('tfm1.file_code', 'tfm1.sender');

        // Apply role filters to Talaq query (same as regular fatawa)
        $uniqueTalaqMahleQuery = $this->applyRoleFilters($uniqueTalaqMahleQuery, $roleInfo);

        // Apply department filters if needed
        if (!empty($daruliftaNames)) {
            $uniqueTalaqMahleQuery->whereIn('tfm1.darulifta_name', $daruliftaNames);
        }

        $uniqueTalaqMahle = $uniqueTalaqMahleQuery->get();

        // Filter out transferred Talaq fatawa (same logic as regular fatawa)
        $filteredTalaqMahle = $uniqueTalaqMahle->filter(function ($record) use ($currentAssignees, $roleInfo) {
            $fileCode = $record->file_code;
            $sender = $record->sender;

            // If this file code is assigned to someone else, exclude it
            if (isset($currentAssignees[$fileCode]) && $currentAssignees[$fileCode] !== $sender) {
                return false;
            }

            return true;
        });

        $talaqMahlenazar = $filteredTalaqMahle->count();

        // Total net mahl-e-nazar count (regular + Talaq)
        $netmahlenazar = $regularMahlenazar + $talaqMahlenazar;

        // Get total not deliver count
        $totalnot_deliverQuery = DB::table('uploaded_files')
            ->where('checked_folder', 'ok')
            ->where('deliver', 0)
            ->whereIn('darulifta_name', $daruliftaNames);
        $totalnot_deliverQuery = $this->applyRoleFilters($totalnot_deliverQuery, $roleInfo);
        $totalnot_deliver = $totalnot_deliverQuery->count();

        // Get total deliver count
        $total_deliverQuery = DB::table('uploaded_files')
            ->where('checked_folder', 'ok')
            ->where('deliver', 1)
            ->whereIn('darulifta_name', $daruliftaNames);
        $total_deliverQuery = $this->applyRoleFilters($total_deliverQuery, $roleInfo);
        $total_deliver = $total_deliverQuery->count();

        return view('dashboard.index', compact(
            'mailfolderDates', 'daruliftaNames', 'summaryReport', 'remainfolderDate', 'unselectedFiles',
            'unselectedTalaq', 'totalQuestionsCount', 'sendToMufti', 'currentmonthrecived',
            'role', 'currentmonthchecked', 'currentmonthTalaqchecked', 'totalmahlenazar',
            'totalokfatawa', 'totalnotsent', 'netmahlenazar', 'totalnot_deliver',
            'recivedfolderDate', 'checkedfolderDate', 'total_deliver', 'todayChecked',
            'totalselectedviral', 'totalsentsviral', 'totalviral', 'totalwupload',
            'monthlyQuestionsCount', 'monthlysendToMufti', 'dataByDaruliftaName'
        ));
    }

    private function determineUserRole()
    {
        $user = Auth::user();
        $userName = $user->name;
        $userRoles = $user->roles->pluck('name')->toArray();
        
        // Set default values
        $result = [
            'isSuperAdmin' => false,
            'isChecker' => false,
            'isMujeeb' => false,
            'userName' => $userName,
            'checkerName' => null,
            'mujeebName' => null,
            'darulName' => null,
            'darulNames' => [], // Array of all departments for mujeeb
            'assignedMujeebs' => [],
        ];
        
        // Check for SuperAdmin role (highest priority)
        if (in_array('SuperAdmin', $userRoles)) {
            $result['isSuperAdmin'] = true;
            return $result;
        }
        
        // Check for Checker role (second priority)
        if (in_array('Checker', $userRoles)) {
            $checker = DB::table('checker')
                ->where('checker_name', $userName)
                ->first();
                
            if ($checker) {
                $result['isChecker'] = true;
                $result['checkerName'] = $userName;
                $result['checkerFolderId'] = $checker->folder_id ?? null;
                return $result;
            }
        }
        
        // Check for Mujeeb role (lowest priority)
        if (in_array('mujeeb', $userRoles)) {
            $mujeebs = DB::table('mujeebs')
                ->where('mujeeb_name', $userName)
                ->get();
                
            if ($mujeebs->isNotEmpty()) {
                $result['isMujeeb'] = true;
                $result['mujeebName'] = $userName;
                $result['darulName'] = $mujeebs->first()->darul_name ?? null; // Keep for backward compatibility
                $result['darulNames'] = $mujeebs->pluck('darul_name')->toArray(); // All departments
                return $result;
            }
        }
        
        // Check if user is in mujeebs table but not assigned the mujeeb role
        $mujeebs = DB::table('mujeebs')
            ->where('mujeeb_name', $userName)
            ->get();
            
        if ($mujeebs->isNotEmpty()) {
            $result['isMujeeb'] = true;
            $result['mujeebName'] = $userName;
            $result['darulName'] = $mujeebs->first()->darul_name ?? null; // Keep for backward compatibility
            $result['darulNames'] = $mujeebs->pluck('darul_name')->toArray(); // All departments
        }
        
        return $result;
    }

    private function applyRoleFilters($query, $roleInfo, $senderColumn = 'sender', $checkerColumn = 'checker')
    {
        // SuperAdmin has full access
        if ($roleInfo['isSuperAdmin']) {
            return $query;
        }
        
        // Get the table name from the query
        $tableName = $query->from;
        
        // For questions table, use assign_id instead of sender and do NOT try to filter by checker
        if ($tableName === 'questions') {
            $senderColumn = 'assign_id';
            $checkerColumn = null; // questions table has no 'checker' column
        }
        
        // Handle table aliases
        if (strpos($tableName, ' as ') !== false) {
            // Extract the alias from the table name
            $alias = explode(' as ', $tableName)[1];
            $senderColumn = $alias . '.' . $senderColumn;
            if (!is_null($checkerColumn)) {
                $checkerColumn = $alias . '.' . $checkerColumn;
            }
        }
        
        if ($roleInfo['isChecker']) {
            // Use the checker's folder_id that was captured during role determination
            $checkerFolderId = $roleInfo['checkerFolderId'] ?? null;
            
            // If checker has both a folder_id and assigned mujeebs, use AND logic
            if ($checkerFolderId && !empty($roleInfo['assignedMujeebs'])) {
                if (!is_null($checkerColumn)) {
                    return $query->where($checkerColumn, $checkerFolderId)
                                 ->whereIn($senderColumn, $roleInfo['assignedMujeebs']);
                }
                return $query->whereIn($senderColumn, $roleInfo['assignedMujeebs']);
            }
            
            // If checker only has a folder_id (no assigned mujeebs)
            elseif ($checkerFolderId) {
                // Only apply checker filter if the target table actually has a checker column
                if (!is_null($checkerColumn)) {
                    return $query->where($checkerColumn, $checkerFolderId);
                }
                // Otherwise, do not restrict by checker for this table
                return $query;
            }
            
            // If checker only has assigned mujeebs (no folder_id)
            elseif (!empty($roleInfo['assignedMujeebs'])) {
                return $query->whereIn($senderColumn, $roleInfo['assignedMujeebs']);
            }
            
            // If checker has neither (unusual case)
            else {
                return $query->where('id', 0);
            }
        }
        
        if ($roleInfo['isMujeeb']) {
            // Mujeeb can only see their own data
            return $query->where($senderColumn, $roleInfo['userName']);
        }
        
        // Default case - don't modify the query
        return $query;
    }

}
