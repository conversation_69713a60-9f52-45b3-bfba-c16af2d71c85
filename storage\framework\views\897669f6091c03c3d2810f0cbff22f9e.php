<div>
    <div wire:ignore>
        <?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show bg-gray-100']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show bg-gray-100']); ?>
            <?php if (isset($component)) { $__componentOriginaleeb4de73933bf6c97cffe74e5846276e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.sidebar','data' => ['activePage' => 'transferred-fatawa']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => 'transferred-fatawa']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $attributes = $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $component = $__componentOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
    </div>

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ps-lg-265 ps-0">
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('navbar', ['titlePage' => 'Transferred Fatawa']);

$__html = app('livewire')->mount($__name, $__params, 'lw-4268376775-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="card my-4 shadow-sm">
                        <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                            <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3 d-flex justify-content-between align-items-center">
                                <h6 class="text-white text-capitalize ps-3 mb-0">
                                    <i class="fas fa-exchange-alt me-2"></i>Transferred Fatawa
                                </h6>
                                <div class="me-3">
                                    <a href="<?php echo e(route('mahlenazar-fatawa')); ?>" class="btn btn-sm bg-gradient-dark mb-0">
                                        <i class="fas fa-arrow-left me-1"></i>Back to Mahl-e-Nazar
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body px-0 pb-2">
                            <!--[if BLOCK]><![endif]--><?php if($successMessage): ?>
                                <div class="alert alert-success alert-dismissible fade show mx-4 mt-3" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo e($successMessage); ?>

                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            
                            <!--[if BLOCK]><![endif]--><?php if($errorMessage): ?>
                                <div class="alert alert-danger alert-dismissible fade show mx-4 mt-3" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo e($errorMessage); ?>

                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            
                            <div class="px-4 my-4">
                                <div class="input-group input-group-dynamic mb-4">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control ps-2" placeholder="Search by Fatwa No, Sender, Darulifta or Category" wire:model.live.debounce.300ms="search">
                                </div>
                            </div>

                            <div class="table-responsive px-4">
                                <table class="table align-items-center table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-uppercase text-dark font-weight-bolder">Fatwa No</th>
                                            <th class="text-uppercase text-dark font-weight-bolder ps-2">Sender</th>
                                            <th class="text-uppercase text-dark font-weight-bolder ps-2">Previous Sender</th>
                                            <th class="text-uppercase text-dark font-weight-bolder ps-2">Darulifta</th>
                                            <th class="text-uppercase text-dark font-weight-bolder ps-2">Mail Folder Date</th>
                                            <th class="text-uppercase text-dark font-weight-bolder ps-2">Category</th>
                                            <th class="text-uppercase text-dark font-weight-bolder ps-2 text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $transferredFatawa; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fatwa): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <tr>
                                                <td class="py-3">
                                                    <div class="d-flex px-2">
                                                        <div class="icon icon-shape icon-sm bg-gradient-primary shadow text-center me-2 d-flex align-items-center justify-content-center rounded-circle">
                                                            <i class="fas fa-file-alt text-white"></i>
                                                        </div>
                                                        <div class="d-flex flex-column justify-content-center">
                                                            <h6 class="mb-0 text-sm"><?php echo e($fatwa->file_code); ?></h6>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="py-3">
                                                    <!--[if BLOCK]><![endif]--><?php if($editFatwaId === $fatwa->id): ?>
                                                        <div class="px-2">
                                                            <select class="form-select form-select-sm <?php $__errorArgs = ['newSender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model="newSender">
                                                                <option value="">-- Select Sender --</option>
                                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mujeebs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mujeeb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($mujeeb); ?>"><?php echo e($mujeeb); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                            </select>
                                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['newSender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                                        </div>
                                                    <?php else: ?>
                                                        <p class="text-sm font-weight-bold mb-0"><?php echo e($fatwa->sender); ?></p>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </td>
                                                <td class="py-3">
                                                    <p class="text-sm font-weight-bold mb-0"><?php echo e($fatwa->transfer_by ?? '-'); ?></p>
                                                </td>
                                                <td class="py-3">
                                                    <p class="text-sm font-weight-bold mb-0"><?php echo e($fatwa->darulifta_name); ?></p>
                                                </td>
                                                <td class="text-center folder-column">
                                                <?php
    $folderDates = [];
    $senderDates = [];
?>
<?php $__currentLoopData = $mahlenazar_null; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mahle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($fatwa->file_code == $mahle->file_code): ?>
        <?php
            $folderDates[] = $mahle->mail_folder_date;
            $senderDates[] = [
                'date' => $mahle->mail_folder_date,
                'sender' => $mahle->sender ?? 'Unknown'
            ];
        ?>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

<!--[if BLOCK]><![endif]--><?php if(count($senderDates) > 0): ?>
    <div class="folder-dates">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $senderDates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="date-entry mb-2">
                <span class="badge">
                    <strong><?php echo e($item['sender']); ?></strong>=>
                    <?php echo e($item['date']); ?>

                </span>
                <div class="action-buttons mt-1">
                    <?php
                        $matchedFileName = null;
                        $darulifta = null;
                        foreach ($mahlenazar_null as $mahle) {
                            $senderMatch = ($mahle->sender ?? 'Unknown') === $item['sender'];
                            if (
                                $mahle->file_code == $fatwa->file_code &&
                                $mahle->mail_folder_date == $item['date'] &&
                                $senderMatch
                            ) {
                                $matchedFileName = $mahle->file_name;
                                $darulifta = $mahle->darulifta_name ?? $fatwa->darulifta_name;
                                break;
                            }
                        }

                        $baseDateParam = $item['date'] . $darulifta;
                        if (empty($mahle->by_mufti)) {
                            $checkerPart = empty($mahle->checker) ? 'Checked' : 'Checked_by_' . $mahle->checker;
                        } else {
                            $checkerPart = 'Checked_by_' . $mahle->checker . '_' . $mahle->by_mufti;
                        }
                        $dateParam = $baseDateParam . $checkerPart;
                   ?>
                    <a href="<?php echo e(route('viewCheck', [
                        'date' => $dateParam,
                        'folder' => 'Mahl-e-Nazar',
                        'filename' => $matchedFileName
                    ])); ?>" target="_blank" class="btn btn-xs btn-primary">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="<?php echo e(route('downloadCheck', [
                        'date' => $dateParam,
                        'filename' => $matchedFileName,
                        'folder' => 'Mahl-e-Nazar'
                    ])); ?>" class="btn btn-xs btn-success">
                        <i class="fas fa-download"></i>
                    </a>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
<?php else: ?>
    <p class="text-sm font-weight-bold mb-0"><?php echo e($fatwa->mail_folder_date); ?></p>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                </td>
                                                <td class="py-3">
                                                    <span class="badge bg-gradient-info"><?php echo e($fatwa->category); ?></span>
                                                </td>
                                                <td class="text-center py-3">
                                                    <div class="d-flex justify-content-center">
                                                        <!--[if BLOCK]><![endif]--><?php if($editFatwaId === $fatwa->id): ?>
                                                            <button class="btn btn-sm btn-success me-2 px-3" wire:click="updateFatwa">
                                                                <i class="fas fa-save me-1"></i>Save
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary px-3" wire:click="cancelEdit">
                                                                <i class="fas fa-times me-1"></i>Cancel
                                                            </button>
                                                        <?php else: ?>
                                                           
                                                            <button class="btn btn-sm btn-info me-2 px-3" wire:click="startEdit(<?php echo e($fatwa->id); ?>)">
                                                                <i class="fas fa-edit me-1"></i>Edit
                                                            </button>
                                                            <button class="btn btn-sm btn-danger px-3" wire:click="confirmDelete(<?php echo e($fatwa->id); ?>)">
                                                                <i class="fas fa-trash me-1"></i>Delete
                                                            </button>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <tr>
                                                <td colspan="6" class="text-center py-5">
                                                    <div class="d-flex flex-column align-items-center">
                                                        <i class="fas fa-folder-open text-secondary mb-3" style="font-size: 3rem;"></i>
                                                        <p class="text-secondary mb-0">No transferred fatawa found.</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="px-4 mt-4">
                                <?php echo e($transferredFatawa->links('pagination::bootstrap-5')); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
</div>

        <!-- Delete Confirmation Modal -->
        <!--[if BLOCK]><![endif]--><?php if($confirmingDelete): ?>
        <div class="modal-backdrop show" style="display: block;"></div>
        <div class="modal fade show" tabindex="-1" role="dialog" style="display: block;">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                        </h5>
                        <button type="button" class="btn-close btn-close-white" wire:click="cancelDelete"></button>
                    </div>
                    <div class="modal-body p-4">
                        <p class="mb-2">Are you sure you want to delete this fatwa?</p>
                        <p class="text-danger mb-0"><i class="fas fa-info-circle me-1"></i>This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer border-top-0">
                        <button type="button" class="btn bg-gradient-secondary" wire:click="cancelDelete">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="button" class="btn bg-gradient-danger" wire:click="deleteFatwa">
                            <i class="fas fa-trash me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <?php if (isset($component)) { $__componentOriginalf30276552b63aa6c9559a1667ce359f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf30276552b63aa6c9559a1667ce359f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footers.auth','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('footers.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $attributes = $__attributesOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $component = $__componentOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__componentOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
    </main>

    <style>
        /* Fix for sidebar overlay issue */
        @media (min-width: 992px) {
            .ps-lg-265 {
                padding-left: 265px !important;
            }
            .g-sidenav-show .sidenav {
                max-width: 250px;
            }
        }

        /* Folder dates styling */
        .folder-dates {
            display: flex;
            flex-direction: column;
            gap: 5px;
            max-height: 250px; /* Increased max height to show more items */
            overflow-y: auto;
        }
        
        .folder-dates .badge {
            background-color: rgba(25, 118, 210, 0.1);
            color: #1976D2;
            font-size: 0.75rem;
            padding: 0.4rem 0.6rem;
            margin: 1px 0;
            text-align: left;
            line-height: 1.2;
            white-space: normal;
        }

        .date-entry {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 8px;
            background-color: #f8f9fa;
        }

        .action-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 5px;
        }

        .btn-xs {
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
            border-radius: 0.25rem;
        }

        /* Enhanced styling */
        .card {
            border: none;
            transition: all 0.3s ease;
        }

        .card-header {
            border-radius: 0.75rem !important;
        }

        .table > :not(:first-child) {
            border-top: none;
        }

        .table thead th {
            font-size: 0.65rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }

        .table tbody tr {
            border-bottom: 1px solid #f0f2f5;
        }

        .table tbody tr:last-child {
            border-bottom: none;
        }

        .bg-gradient-primary {
            background-image: linear-gradient(195deg, #49a3f1 0%, #1A73E8 100%);
        }

        .bg-gradient-dark {
            background-image: linear-gradient(195deg, #42424a 0%, #191919 100%);
        }

        .bg-gradient-success {
            background-image: linear-gradient(195deg, #66BB6A 0%, #43A047 100%);
        }

        .bg-gradient-info {
            background-image: linear-gradient(195deg, #49a3f1 0%, #1A73E8 100%);
        }

        .bg-gradient-danger {
            background-image: linear-gradient(195deg, #ec407a 0%, #D81B60 100%);
        }

        /* Improved form controls */
        .form-control, .form-select {
            border: 1px solid #d2d6da;
            padding: 0.6rem 1rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-radius: 0.5rem;
            background-color: #fff;
            transition: box-shadow 0.15s ease, border-color 0.15s ease;
        }

        .input-group-dynamic {
            position: relative;
        }

        .input-group-dynamic .input-group-text {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            background: transparent;
            border: none;
            color: #6c757d;
        }

        .input-group-dynamic .form-control {
            padding-left: 40px;
        }

        /* Button styling */
        .btn {
            text-transform: none;
            font-weight: 500;
            letter-spacing: 0;
            transition: all 0.2s ease;
            box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 6px rgba(0, 0, 0, 0.15);
        }

        .btn-sm {
            padding: 0.4rem 1rem;
            font-size: 0.75rem;
            border-radius: 0.5rem;
        }

        /* Icon styling */
        .icon-shape {
            width: 32px;
            height: 32px;
            background-position: center;
            border-radius: 0.5rem;
        }

        /* Badge styling */
        .badge {
            padding: 0.55em 0.9em;
            font-weight: 500;
            font-size: 0.75em;
            border-radius: 0.5rem;
        }

        /* Folder column styling */
        .folder-column {
            vertical-align: middle;
        }

        .folder-column .folder-dates {
            display: flex;
            flex-direction: column;
            gap: 5px;
            max-height: 150px; /* Increased max height to show more items */
            overflow-y: auto;
        }

        .folder-column .badge {
            display: inline-block;
            font-size: 0.75rem;
            padding: 0.4rem 0.6rem;
            margin: 1px 0;
            text-align: left;
            line-height: 1.2;
            white-space: normal;
        }

        /* Alerts */
        .alert {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .alert-success {
            background-color: rgba(102, 187, 106, 0.1);
            color: #43A047;
        }

        .alert-danger {
            background-color: rgba(244, 67, 54, 0.1);
            color: #E53935;
        }

        /* Modal styling */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .modal-header {
            border-top-left-radius: 0.75rem;
            border-top-right-radius: 0.75rem;
        }

        /* Pagination styling */
        .pagination {
            --bs-pagination-color: #1A73E8;
            --bs-pagination-active-bg: #1A73E8;
            --bs-pagination-active-border-color: #1A73E8;
        }

        /* Adding Font Awesome support */
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');
    </style>
</div><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/transferred-fatawa.blade.php ENDPATH**/ ?>